package logic

import (
	"context"

	"mp/rpc/internal/svc"
	"mp/rpc/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateTagLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewUpdateTagLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateTagLogic {
	return &UpdateTagLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *UpdateTagLogic) UpdateTag(in *member.UpdateTagReq) (*member.TagInfo, error) {
	// todo: add your logic here and delete this line

	return &member.TagInfo{}, nil
}
