// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5

package types

type AddTagsToMemberReq struct {
	MemberId int64   `json:"memberId"`
	TagIds   []int64 `json:"tagIds"`
}

type CommonResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

type CreateMemberReq struct {
	MerchantId          int64  `json:"merchantId,optional"`
	StoreId             int64  `json:"storeId,optional"`
	Username            string `json:"username"`
	Password            string `json:"password"`
	Type                int    `json:"type,optional"`
	Realname            string `json:"realname,optional"`
	Nickname            string `json:"nickname,optional"`
	HeadPortrait        string `json:"headPortrait,optional"`
	Gender              int    `json:"gender,optional"`
	Qq                  string `json:"qq,optional"`
	Email               string `json:"email,optional"`
	Birthday            string `json:"birthday,optional"`
	ProvinceId          int64  `json:"provinceId,optional"`
	CityId              int64  `json:"cityId,optional"`
	AreaId              int64  `json:"areaId,optional"`
	Address             string `json:"address,optional"`
	Mobile              string `json:"mobile,optional"`
	TelNo               string `json:"telNo,optional"`
	BgImage             string `json:"bgImage,optional"`
	Description         string `json:"description,optional"`
	Role                int    `json:"role,optional"`
	CurrentLevel        int    `json:"currentLevel,optional"`
	LevelExpirationTime int64  `json:"levelExpirationTime,optional"`
	LevelBuyType        int    `json:"levelBuyType,optional"`
	Pid                 int64  `json:"pid,optional"`
	Level               int    `json:"level,optional"`
	Tree                string `json:"tree,optional"`
	PromoterCode        string `json:"promoterCode,optional"`
	CertificationType   int    `json:"certificationType,optional"`
	Source              string `json:"source,optional"`
	Status              int    `json:"status,optional"`
	RegionId            int64  `json:"regionId,optional"`
}

type CreateTagReq struct {
	MerchantId int64  `json:"merchantId"`
	StoreId    int64  `json:"storeId,optional"`
	Title      string `json:"title"`
	Sort       int    `json:"sort,optional"`
	Status     int    `json:"status,optional"`
}

type DeleteMemberReq struct {
	Id int64 `path:"id"`
}

type DeleteTagReq struct {
	Id int64 `path:"id"`
}

type GetMemberReq struct {
	Id int64 `path:"id"`
}

type GetMemberTagsReq struct {
	MemberId int64 `path:"memberId"`
}

type GetTagReq struct {
	Id int64 `path:"id"`
}

type ListMembersReq struct {
	PageRequest
	MerchantId int64  `form:"merchantId,optional"`
	StoreId    int64  `form:"storeId,optional"`
	Keyword    string `form:"keyword,optional"`
	Status     int    `form:"status,optional"`
}

type ListMembersResp struct {
	Total int64    `json:"total"`
	List  []Member `json:"list"`
}

type ListTagsReq struct {
	PageRequest
	MerchantId int64  `form:"merchantId,optional"`
	StoreId    int64  `form:"storeId,optional"`
	Keyword    string `form:"keyword,optional"`
	Status     int    `form:"status,optional"`
}

type ListTagsResp struct {
	Total int64 `json:"total"`
	List  []Tag `json:"list"`
}

type Member struct {
	Id                  int64  `json:"id"`
	MerchantId          int64  `json:"merchantId"`
	StoreId             int64  `json:"storeId"`
	Username            string `json:"username"`
	PasswordHash        string `json:"passwordHash,omitempty"`
	AuthKey             string `json:"authKey,omitempty"`
	PasswordResetToken  string `json:"passwordResetToken,omitempty"`
	MobileResetToken    string `json:"mobileResetToken,omitempty"`
	Type                int    `json:"type,omitempty"`
	Realname            string `json:"realname"`
	Nickname            string `json:"nickname"`
	HeadPortrait        string `json:"headPortrait"`
	Gender              int    `json:"gender"`
	Qq                  string `json:"qq"`
	Email               string `json:"email"`
	Birthday            string `json:"birthday"`
	ProvinceId          int64  `json:"provinceId"`
	CityId              int64  `json:"cityId"`
	AreaId              int64  `json:"areaId"`
	Address             string `json:"address"`
	Mobile              string `json:"mobile"`
	TelNo               string `json:"telNo"`
	BgImage             string `json:"bgImage"`
	Description         string `json:"description"`
	VisitCount          int    `json:"visitCount"`
	LastTime            int64  `json:"lastTime"`
	LastIp              string `json:"lastIp"`
	Role                int    `json:"role"`
	CurrentLevel        int    `json:"currentLevel"`
	LevelExpirationTime int64  `json:"levelExpirationTime"`
	LevelBuyType        int    `json:"levelBuyType"`
	Pid                 int64  `json:"pid"`
	Level               int    `json:"level"`
	Tree                string `json:"tree"`
	PromoterCode        string `json:"promoterCode"`
	CertificationType   int    `json:"certificationType"`
	Source              string `json:"source"`
	Status              int    `json:"status"`
	CreatedAt           int64  `json:"createdAt"`
	UpdatedAt           int64  `json:"updatedAt"`
	RegionId            int64  `json:"regionId"`
}

type PageRequest struct {
	Page     int `form:"page,default=1"`
	PageSize int `form:"pageSize,default=20"`
}

type RemoveTagsFromMemberReq struct {
	MemberId int64   `json:"memberId"`
	TagIds   []int64 `json:"tagIds"`
}

type Tag struct {
	Id         int64  `json:"id"`
	MerchantId int64  `json:"merchantId"`
	StoreId    int64  `json:"storeId"`
	Title      string `json:"title"`
	Sort       int    `json:"sort"`
	Status     int    `json:"status"`
	CreatedAt  int64  `json:"createdAt"`
	UpdatedAt  int64  `json:"updatedAt"`
}

type UpdateMemberReq struct {
	Id                  int64  `path:"id"`
	MerchantId          int64  `json:"merchantId,optional"`
	StoreId             int64  `json:"storeId,optional"`
	Username            string `json:"username,optional"`
	Type                int    `json:"type,optional"`
	Realname            string `json:"realname,optional"`
	Nickname            string `json:"nickname,optional"`
	HeadPortrait        string `json:"headPortrait,optional"`
	Gender              int    `json:"gender,optional"`
	Qq                  string `json:"qq,optional"`
	Email               string `json:"email,optional"`
	Birthday            string `json:"birthday,optional"`
	ProvinceId          int64  `json:"provinceId,optional"`
	CityId              int64  `json:"cityId,optional"`
	AreaId              int64  `json:"areaId,optional"`
	Address             string `json:"address,optional"`
	Mobile              string `json:"mobile,optional"`
	TelNo               string `json:"telNo,optional"`
	BgImage             string `json:"bgImage,optional"`
	Description         string `json:"description,optional"`
	VisitCount          int    `json:"visitCount,optional"`
	LastTime            int64  `json:"lastTime,optional"`
	LastIp              string `json:"lastIp,optional"`
	Role                int    `json:"role,optional"`
	CurrentLevel        int    `json:"currentLevel,optional"`
	LevelExpirationTime int64  `json:"levelExpirationTime,optional"`
	LevelBuyType        int    `json:"levelBuyType,optional"`
	Pid                 int64  `json:"pid,optional"`
	Level               int    `json:"level,optional"`
	Tree                string `json:"tree,optional"`
	PromoterCode        string `json:"promoterCode,optional"`
	CertificationType   int    `json:"certificationType,optional"`
	Source              string `json:"source,optional"`
	Status              int    `json:"status,optional"`
	RegionId            int64  `json:"regionId,optional"`
}

type UpdateTagReq struct {
	Id     int64  `path:"id"`
	Title  string `json:"title,optional"`
	Sort   int    `json:"sort,optional"`
	Status int    `json:"status,optional"`
}
