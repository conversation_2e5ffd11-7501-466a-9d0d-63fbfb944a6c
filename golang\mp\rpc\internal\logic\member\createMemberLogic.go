package member

import (
	"context"
	"fmt"
	"mp/rpc/model"
	"time"

	"mp/rpc/internal/svc"
	"mp/rpc/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateMemberLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateMemberLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateMemberLogic {
	return &CreateMemberLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *CreateMemberLogic) CreateMember(in *member.CreateMemberReq) (*member.MemberInfo, error) {
	// todo: add your logic here and delete this line


	if in.Username == "" {
		return nil, fmt.Errorf("用户名不能为空")
	}
	if in.Password == "" {
		return nil, fmt.<PERSON><PERSON><PERSON>("密码不能为空")
	}

	// 处理生日字段的转换
	var birthday *time.Time
	if in.Birthday != "" {
		// 尝试解析生日字符串，假设格式为 "2006-01-02"
		if parsedTime, err := time.Parse("2006-01-02", in.Birthday); err == nil {
			birthday = &parsedTime
		} else {
			l.Errorf("解析生日字段失败: %v", err)
		}
	}

	memberData := &model.Member{
		MerchantID:        uint(in.MerchantId),
		StoreID:           uint(in.StoreId),
		Username:          in.Username,
		PasswordHash:      in.Password,
		Realname:          in.Realname,
		Nickname:          in.Nickname,
		HeadPortrait:      in.HeadPortrait,
		Gender:            uint8(in.Gender),
		QQ:                in.Qq,
		Email:             in.Email,
		Birthday:          birthday,
		ProvinceID:        uint(in.ProvinceId),
		CityID:            uint(in.CityId),
		AreaID:            uint(in.AreaId),
		Address:           in.Address,
		Mobile:            in.Mobile,
		TelNo:             in.TelNo,
		BgImage:           in.BgImage,
		Description:       in.Description,
		Role:              int16(in.Role),
		CurrentLevel:      int8(in.CurrentLevel),
		LevelBuyType:      int8(in.LevelBuyType),
		Pid:               uint(in.Pid),
		CertificationType: int8(in.CertificationType),
		Source:            in.Source,
		Status:            int8(in.Status),
		CreatedAt:         uint(time.Now().Unix()),
		UpdatedAt:         uint(time.Now().Unix()),
		RegionID:          uint(in.RegionId),
	}

	// 创建会员记录
	if err := l.svcCtx.DB.Create(memberData).Error; err != nil {
		l.Errorf("创建会员失败: %v", err)
		return nil, fmt.Errorf("创建会员失败: %v", err)
	}

	// 返回创建的会员信息
	return &member.MemberInfo{
		Id:                  int64(memberData.ID),
		MerchantId:          int64(memberData.MerchantID),
		StoreId:             int64(memberData.StoreID),
		Username:            memberData.Username,
		Realname:            memberData.Realname,
		Nickname:            memberData.Nickname,
		HeadPortrait:        memberData.HeadPortrait,
		Gender:              int32(memberData.Gender),
		Qq:                  memberData.QQ,
		Email:               memberData.Email,
		Birthday:            in.Birthday,
		ProvinceId:          int64(memberData.ProvinceID),
		CityId:              int64(memberData.CityID),
		AreaId:              int64(memberData.AreaID),
		Address:             memberData.Address,
		Mobile:              memberData.Mobile,
		TelNo:               memberData.TelNo,
		BgImage:             memberData.BgImage,
		Description:         memberData.Description,
		VisitCount:          int32(memberData.VisitCount),
		LastTime:            int64(memberData.LastTime),
		LastIp:              memberData.LastIP,
		Role:                int32(memberData.Role),
		CurrentLevel:        int32(memberData.CurrentLevel),
		LevelExpirationTime: int64(memberData.LevelExpirationTime),
		LevelBuyType:        int32(memberData.LevelBuyType),
		Pid:                 int64(memberData.Pid),
		Level:               int32(memberData.Level),
		Tree:                memberData.Tree,
		PromoterCode:        memberData.PromoterCode,
		CertificationType:   int32(memberData.CertificationType),
		Source:              memberData.Source,
		Status:              int32(memberData.Status),
		CreatedAt:           int64(memberData.CreatedAt),
		UpdatedAt:           int64(memberData.UpdatedAt),
		RegionId:            int64(memberData.RegionID),
	}, nil
}
