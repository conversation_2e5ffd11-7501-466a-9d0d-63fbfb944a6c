package logic

import (
	"context"

	"mp/rpc/internal/svc"
	"mp/rpc/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type RemoveTagsFromMemberLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewRemoveTagsFromMemberLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RemoveTagsFromMemberLogic {
	return &RemoveTagsFromMemberLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *RemoveTagsFromMemberLogic) RemoveTagsFromMember(in *member.RemoveTagsFromMemberReq) (*member.CommonResp, error) {
	// todo: add your logic here and delete this line

	return &member.CommonResp{}, nil
}
