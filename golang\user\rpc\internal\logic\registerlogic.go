package logic

import (
	"context"
	"fmt"
	"mp/rpc/member"

	// "../../../../mp/rpc/model"
	"user/rpc/internal/svc"
	"user/rpc/middleware/JWT"
	"user/rpc/user"

	"github.com/zeromicro/go-zero/core/logx"
)

type RegisterLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewRegisterLogic(ctx context.Context, svcCtx *svc.ServiceContext) *RegisterLogic {
	return &RegisterLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// Register creates a new user
func (l *RegisterLogic) Register(in *user.RegisterRequest) (*user.UserResponse, error) {
	// 在注册之前先检查该用户是否已经注册过
	existUser, err := l.svcCtx.MemberRpc.GetMember(l.ctx, &member.GetMemberReq{
		Username: in.Username,
	})

	if err == nil && existUser != nil {
		return nil, fmt.Errorf("用户名已存在，请登录")
	}

	// 用户名不存在开始进行注册用户
	userData := &model.Member{
		Username: in.Username,
		Password: in.Password,
		Email:    in.Email,
		Phone:    in.Phone,
	}
	// 使用gorm框架进行对密码进行加密
	err = l.svcCtx.UserModel.CreateUser(userData, l.svcCtx.Config.Auth.AccessSecret)

	if err != nil {
		return nil, fmt.Errorf("注册失败，请重新注册: %v", err)
	}

	// 调用member的rpc服务进行注册
	_, err = l.svcCtx.MemberRpc.CreateMember(l.ctx, userData)

	if err != nil {
		return nil, fmt.Errorf("注册失败，请重新注册: %v", err)
	}

	existUser, err = l.svcCtx.MemberRpc.GetMember(l.ctx, &member.GetMemberReq{
		Username: in.Username,
	})

	if err == nil && existUser != nil {
		return nil, fmt.Errorf("查询数据库失败")
	}

	token, err := JWT.GetJwtToken(l.svcCtx.Config.Auth.AccessSecret, 0, l.svcCtx.Config.Auth.AccessExpire, existUser.Username)

	if err != nil {
		return nil, fmt.Errorf("生成token失败: %v", err)
	}

	// 返回数据给api服务
	return &user.UserResponse{
		Username: existUser.Username,
		Email:    existUser.Email,
		Phone:    existUser.Mobile,
		Token:    token,
		Code:     200,
		Message:  "注册成功",
	}, nil
}
