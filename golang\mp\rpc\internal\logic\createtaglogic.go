package logic

import (
	"context"

	"mp/rpc/internal/svc"
	"mp/rpc/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateTagLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewCreateTagLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateTagLogic {
	return &CreateTagLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *CreateTagLogic) CreateTag(in *member.CreateTagReq) (*member.TagInfo, error) {
	// todo: add your logic here and delete this line

	return &member.TagInfo{}, nil
}
