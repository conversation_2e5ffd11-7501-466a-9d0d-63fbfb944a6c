// Code generated by goctl. DO NOT EDIT.
// goctl 1.8.5
// Source: member.proto

package server

import (
	"context"

	"mp/rpc/internal/logic"
	"mp/rpc/internal/svc"
	"mp/rpc/member"
)

type MemberServer struct {
	svcCtx *svc.ServiceContext
	member.UnimplementedMemberServer
}

func NewMemberServer(svcCtx *svc.ServiceContext) *MemberServer {
	return &MemberServer{
		svcCtx: svcCtx,
	}
}

// 会员管理
func (s *MemberServer) GetMember(ctx context.Context, in *member.GetMemberReq) (*member.MemberInfo, error) {
	l := logic.NewGetMemberLogic(ctx, s.svcCtx)
	return l.GetMember(in)
}

func (s *MemberServer) ListMembers(ctx context.Context, in *member.ListMembersReq) (*member.ListMembersResp, error) {
	l := logic.NewListMembersLogic(ctx, s.svcCtx)
	return l.ListMembers(in)
}

func (s *MemberServer) CreateMember(ctx context.Context, in *member.CreateMemberReq) (*member.MemberInfo, error) {
	l := logic.NewCreateMemberLogic(ctx, s.svcCtx)
	return l.CreateMember(in)
}

func (s *MemberServer) UpdateMember(ctx context.Context, in *member.UpdateMemberReq) (*member.MemberInfo, error) {
	l := logic.NewUpdateMemberLogic(ctx, s.svcCtx)
	return l.UpdateMember(in)
}

func (s *MemberServer) DeleteMember(ctx context.Context, in *member.DeleteMemberReq) (*member.CommonResp, error) {
	l := logic.NewDeleteMemberLogic(ctx, s.svcCtx)
	return l.DeleteMember(in)
}

// 标签管理
func (s *MemberServer) GetTag(ctx context.Context, in *member.GetTagReq) (*member.TagInfo, error) {
	l := logic.NewGetTagLogic(ctx, s.svcCtx)
	return l.GetTag(in)
}

func (s *MemberServer) ListTags(ctx context.Context, in *member.ListTagsReq) (*member.ListTagsResp, error) {
	l := logic.NewListTagsLogic(ctx, s.svcCtx)
	return l.ListTags(in)
}

func (s *MemberServer) CreateTag(ctx context.Context, in *member.CreateTagReq) (*member.TagInfo, error) {
	l := logic.NewCreateTagLogic(ctx, s.svcCtx)
	return l.CreateTag(in)
}

func (s *MemberServer) UpdateTag(ctx context.Context, in *member.UpdateTagReq) (*member.TagInfo, error) {
	l := logic.NewUpdateTagLogic(ctx, s.svcCtx)
	return l.UpdateTag(in)
}

func (s *MemberServer) DeleteTag(ctx context.Context, in *member.DeleteTagReq) (*member.CommonResp, error) {
	l := logic.NewDeleteTagLogic(ctx, s.svcCtx)
	return l.DeleteTag(in)
}

// 会员标签关联
func (s *MemberServer) AddTagsToMember(ctx context.Context, in *member.AddTagsToMemberReq) (*member.CommonResp, error) {
	l := logic.NewAddTagsToMemberLogic(ctx, s.svcCtx)
	return l.AddTagsToMember(in)
}

func (s *MemberServer) RemoveTagsFromMember(ctx context.Context, in *member.RemoveTagsFromMemberReq) (*member.CommonResp, error) {
	l := logic.NewRemoveTagsFromMemberLogic(ctx, s.svcCtx)
	return l.RemoveTagsFromMember(in)
}

func (s *MemberServer) GetMemberTags(ctx context.Context, in *member.GetMemberTagsReq) (*member.ListTagsResp, error) {
	l := logic.NewGetMemberTagsLogic(ctx, s.svcCtx)
	return l.GetMemberTags(in)
}
