package logic

import (
	"context"

	"mp/rpc/internal/svc"
	"mp/rpc/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddTagsToMemberLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewAddTagsToMemberLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddTagsToMemberLogic {
	return &AddTagsToMemberLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

// 会员标签关联
func (l *AddTagsToMemberLogic) AddTagsToMember(in *member.AddTagsToMemberReq) (*member.CommonResp, error) {
	// todo: add your logic here and delete this line

	return &member.CommonResp{}, nil
}
