package logic

import (
	"context"

	"mp/rpc/internal/svc"
	"mp/rpc/member"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetMemberTagsLogic struct {
	ctx    context.Context
	svcCtx *svc.ServiceContext
	logx.Logger
}

func NewGetMemberTagsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMemberTagsLogic {
	return &GetMemberTagsLogic{
		ctx:    ctx,
		svcCtx: svcCtx,
		Logger: logx.WithContext(ctx),
	}
}

func (l *GetMemberTagsLogic) GetMemberTags(in *member.GetMemberTagsReq) (*member.ListTagsResp, error) {
	// todo: add your logic here and delete this line

	return &member.ListTagsResp{}, nil
}
